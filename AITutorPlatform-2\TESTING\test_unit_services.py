"""
Unit Tests for Service Modules
Tests individual functions and components in isolation for backend services.
"""
import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timezone

from services.user_management_service import UserManagementService
from services.academic_service import AcademicService
from models import User, UserRole, Department, Course, Enrollment, EnrollmentStatus


class TestUserManagementService:
    """Test cases for UserManagementService"""

    @pytest.fixture
    def user_service(self):
        """Create UserManagementService instance"""
        return UserManagementService()

    def test_get_all_users_active_only(self, db_session, user_service, sample_user, sample_admin):
        """Test getting all active users"""
        # Act
        users = user_service.get_all_users(db_session, active_only=True)
        
        # Assert
        assert len(users) >= 2  # At least our test users
        user_emails = [user["email"] for user in users]
        assert sample_user.email in user_emails
        assert sample_admin.email in user_emails
        
        # Check user structure
        for user in users:
            assert "id" in user
            assert "name" in user
            assert "email" in user
            assert "role" in user
            assert "is_active" in user

    def test_get_users_by_role_student(self, db_session, user_service, sample_user):
        """Test getting users filtered by student role"""
        # Act
        students = user_service.get_users_by_role(db_session, UserRole.STUDENT)
        
        # Assert
        assert len(students) >= 1
        student_emails = [student["email"] for student in students]
        assert sample_user.email in student_emails
        
        # All returned users should be students
        for student in students:
            assert student["role"] == "student"

    def test_get_users_by_role_admin(self, db_session, user_service, sample_admin):
        """Test getting users filtered by admin role"""
        # Act
        admins = user_service.get_users_by_role(db_session, UserRole.ADMIN)
        
        # Assert
        assert len(admins) >= 1
        admin_emails = [admin["email"] for admin in admins]
        assert sample_admin.email in admin_emails
        
        # All returned users should be admins
        for admin in admins:
            assert admin["role"] == "admin"

    def test_update_user_success(self, db_session, user_service, sample_user):
        """Test successful user update"""
        # Arrange
        update_data = {
            "name": "Updated Name",
            "phone": "************",
            "address": "123 Test Street"
        }
        
        # Act
        updated_user = user_service.update_user(db_session, sample_user.id, update_data)
        
        # Assert
        assert updated_user["name"] == "Updated Name"
        assert updated_user["id"] == sample_user.id
        assert updated_user["email"] == sample_user.email  # Should remain unchanged

    def test_update_user_role(self, db_session, user_service, sample_user):
        """Test updating user role"""
        # Arrange
        update_data = {"role": "lecturer"}
        
        # Act
        updated_user = user_service.update_user(db_session, sample_user.id, update_data)
        
        # Assert
        assert updated_user["role"] == "lecturer"

    def test_update_user_invalid_role(self, db_session, user_service, sample_user):
        """Test updating user with invalid role"""
        # Arrange
        update_data = {"role": "invalid_role"}
        
        # Act & Assert
        with pytest.raises(ValueError, match="Invalid role"):
            user_service.update_user(db_session, sample_user.id, update_data)

    def test_update_user_not_found(self, db_session, user_service):
        """Test updating non-existent user"""
        # Arrange
        update_data = {"name": "New Name"}
        
        # Act & Assert
        with pytest.raises(ValueError, match="User not found"):
            user_service.update_user(db_session, 99999, update_data)

    def test_delete_user_success(self, db_session, user_service, sample_user):
        """Test successful user deletion (soft delete)"""
        # Act
        result = user_service.delete_user(db_session, sample_user.id)
        
        # Assert
        assert result is True
        
        # Verify user is soft deleted
        db_session.refresh(sample_user)
        assert sample_user.is_active is False

    def test_delete_user_not_found(self, db_session, user_service):
        """Test deleting non-existent user"""
        # Act & Assert
        with pytest.raises(ValueError, match="User not found"):
            user_service.delete_user(db_session, 99999)

    def test_get_user_profile(self, db_session, user_service, sample_user):
        """Test getting user profile"""
        # Act
        profile = user_service.get_user_profile(db_session, sample_user.id)
        
        # Assert
        assert profile["id"] == sample_user.id
        assert profile["name"] == sample_user.name
        assert profile["email"] == sample_user.email
        assert profile["role"] == sample_user.role.value


class TestAcademicService:
    """Test cases for AcademicService"""

    @pytest.fixture
    def academic_service(self):
        """Create AcademicService instance"""
        return AcademicService()

    def test_get_departments(self, db_session, academic_service, sample_department):
        """Test getting all departments"""
        # Act
        departments = academic_service.get_departments(db_session)
        
        # Assert
        assert len(departments) >= 1
        dept_names = [dept["name"] for dept in departments]
        assert sample_department.name in dept_names

    def test_create_department_success(self, db_session, academic_service):
        """Test successful department creation"""
        # Arrange
        dept_data = {
            "name": "Mathematics",
            "code": "MATH",
            "description": "Department of Mathematics"
        }
        
        # Act
        department = academic_service.create_department(db_session, dept_data)
        
        # Assert
        assert department["name"] == "Mathematics"
        assert department["code"] == "MATH"
        assert department["description"] == "Department of Mathematics"
        assert department["is_active"] is True

    def test_create_department_duplicate_code(self, db_session, academic_service, sample_department):
        """Test creating department with duplicate code"""
        # Arrange
        dept_data = {
            "name": "Another CS Department",
            "code": sample_department.code,  # Duplicate code
            "description": "Another CS department"
        }
        
        # Act & Assert
        with pytest.raises(ValueError, match="Department code already exists"):
            academic_service.create_department(db_session, dept_data)

    def test_update_department_success(self, db_session, academic_service, sample_department):
        """Test successful department update"""
        # Arrange
        update_data = {
            "name": "Updated Computer Science",
            "description": "Updated description"
        }
        
        # Act
        updated_dept = academic_service.update_department(db_session, sample_department.id, update_data)
        
        # Assert
        assert updated_dept["name"] == "Updated Computer Science"
        assert updated_dept["description"] == "Updated description"
        assert updated_dept["code"] == sample_department.code  # Should remain unchanged

    def test_delete_department_success(self, db_session, academic_service, sample_department):
        """Test successful department deletion"""
        # Act
        academic_service.delete_department(db_session, sample_department.id)
        
        # Assert
        db_session.refresh(sample_department)
        assert sample_department.is_active is False

    def test_get_courses(self, db_session, academic_service, sample_course):
        """Test getting all courses"""
        # Act
        courses = academic_service.get_courses(db_session)
        
        # Assert
        assert len(courses) >= 1
        course_names = [course["name"] for course in courses]
        assert sample_course.name in course_names

    def test_get_courses_by_lecturer(self, db_session, academic_service, sample_course, sample_lecturer):
        """Test getting courses filtered by lecturer"""
        # Act
        courses = academic_service.get_courses(db_session, lecturer_id=sample_lecturer.id)
        
        # Assert
        assert len(courses) >= 1
        for course in courses:
            assert course["lecturer_id"] == sample_lecturer.id

    def test_get_academic_overview(self, db_session, academic_service, sample_user, sample_lecturer, sample_course, sample_department):
        """Test getting academic overview statistics"""
        # Act
        overview = academic_service.get_academic_overview(db_session)
        
        # Assert
        assert "total_students" in overview
        assert "total_lecturers" in overview
        assert "total_courses" in overview
        assert "total_departments" in overview
        assert overview["total_students"] >= 1
        assert overview["total_lecturers"] >= 1
        assert overview["total_courses"] >= 1
        assert overview["total_departments"] >= 1

    def test_enroll_student_success(self, db_session, academic_service, sample_user, sample_course):
        """Test successful student enrollment"""
        # Arrange
        program_id = 1  # Mock program ID
        
        # Act
        result = academic_service.enroll_student(db_session, sample_user.id, sample_course.id, program_id)
        
        # Assert
        assert result["success"] is True
        assert "enrollment_id" in result

    def test_get_student_enrollments(self, db_session, academic_service, sample_user, sample_course):
        """Test getting student enrollments"""
        # Arrange - First enroll the student
        program_id = 1
        academic_service.enroll_student(db_session, sample_user.id, sample_course.id, program_id)
        
        # Act
        enrollments = academic_service.get_student_enrollments(db_session, sample_user.id)
        
        # Assert
        assert len(enrollments) >= 1
        enrollment_course_ids = [enrollment["course_id"] for enrollment in enrollments]
        assert sample_course.id in enrollment_course_ids


class TestServiceIntegration:
    """Test integration between different services"""

    def test_user_service_with_academic_service(self, db_session, sample_lecturer, sample_course):
        """Test integration between user management and academic services"""
        # Arrange
        user_service = UserManagementService()
        academic_service = AcademicService()
        
        # Act - Get lecturer dashboard which uses both services
        dashboard = user_service.get_lecturer_dashboard(db_session, sample_lecturer.id)
        
        # Assert
        assert "courses" in dashboard
        assert "total_students" in dashboard
        assert "recent_submissions" in dashboard

    def test_academic_service_user_dependencies(self, db_session, sample_user, sample_course):
        """Test academic service operations that depend on user data"""
        # Arrange
        academic_service = AcademicService()
        
        # Act - Enroll student (requires valid user)
        result = academic_service.enroll_student(db_session, sample_user.id, sample_course.id, 1)
        
        # Assert
        assert result["success"] is True
        
        # Verify enrollment exists
        enrollments = academic_service.get_student_enrollments(db_session, sample_user.id)
        assert len(enrollments) >= 1
