"""
Security Tests for AI-powered LMS
Tests the robust authentication and authorization system using JWTs and role-based access control.
"""
import pytest
import jwt
from datetime import datetime, timezone, timedelta
from unittest.mock import patch, Mock

from auth import Auth<PERSON>anager, JWT_SECRET_KEY, JWT_ALGORITHM
from models import User, UserRole


class TestJWTSecurityTests:
    """Test JWT token security implementation"""

    def test_jwt_token_structure_security(self, auth_manager):
        """Test JWT token structure and security claims"""
        # Arrange
        user_id = 12345
        
        # Act
        access_token = auth_manager.create_access_token(user_id)
        refresh_token = auth_manager.create_refresh_token(user_id)
        
        # Assert - Decode tokens to verify structure
        access_payload = jwt.decode(access_token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        refresh_payload = jwt.decode(refresh_token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        
        # Verify required claims exist
        assert "sub" in access_payload  # Subject (user_id)
        assert "exp" in access_payload  # Expiration
        assert "type" in access_payload  # Token type
        
        assert "sub" in refresh_payload
        assert "exp" in refresh_payload
        assert "type" in refresh_payload
        
        # Verify token types are correct
        assert access_payload["type"] == "access"
        assert refresh_payload["type"] == "refresh"

    def test_jwt_token_expiration_security(self, auth_manager):
        """Test JWT token expiration handling"""
        # Arrange
        user_id = 54321
        
        # Create token that expires in 1 second
        short_expire = timedelta(seconds=1)
        payload = {
            "sub": str(user_id),
            "exp": datetime.now(timezone.utc) + short_expire,
            "type": "access"
        }
        short_token = jwt.encode(payload, JWT_SECRET_KEY, algorithm=JWT_ALGORITHM)
        
        # Act - Verify token immediately (should work)
        user_id_1, token_type_1 = auth_manager.verify_token(short_token)
        
        # Wait for token to expire
        import time
        time.sleep(2)
        
        # Act - Verify expired token (should fail)
        user_id_2, token_type_2 = auth_manager.verify_token(short_token)
        
        # Assert
        assert user_id_1 == user_id  # Should work before expiration
        assert token_type_1 == "access"
        assert user_id_2 is None  # Should fail after expiration
        assert token_type_2 is None

    def test_jwt_token_tampering_security(self, auth_manager):
        """Test JWT token tampering detection"""
        # Arrange
        user_id = 99999
        token = auth_manager.create_access_token(user_id)
        
        # Act - Tamper with token by changing a character
        tampered_token = token[:-5] + "XXXXX"
        
        # Try to verify tampered token
        verified_user_id, token_type = auth_manager.verify_token(tampered_token)
        
        # Assert
        assert verified_user_id is None
        assert token_type is None

    def test_jwt_secret_key_security(self, auth_manager):
        """Test JWT secret key validation"""
        # Arrange
        user_id = 11111
        token = auth_manager.create_access_token(user_id)
        
        # Act - Try to decode with wrong secret key
        with pytest.raises(jwt.InvalidTokenError):
            jwt.decode(token, "wrong_secret_key", algorithms=[JWT_ALGORITHM])
        
        # Act - Decode with correct secret key (should work)
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        
        # Assert
        assert payload["sub"] == str(user_id)

    def test_jwt_algorithm_security(self, auth_manager):
        """Test JWT algorithm validation"""
        # Arrange
        user_id = 22222
        token = auth_manager.create_access_token(user_id)
        
        # Act & Assert - Try to decode with wrong algorithm
        with pytest.raises(jwt.InvalidTokenError):
            jwt.decode(token, JWT_SECRET_KEY, algorithms=["HS512"])  # Wrong algorithm
        
        # Should work with correct algorithm
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[JWT_ALGORITHM])
        assert payload["sub"] == str(user_id)


class TestRoleBasedAccessControl:
    """Test role-based access control (RBAC) implementation"""

    def test_student_access_restrictions(self, client, sample_user, auth_manager):
        """Test that students can only access student-specific endpoints"""
        # Arrange
        student_headers = {"Authorization": f"Bearer {auth_manager.create_access_token(sample_user.id)}"}
        
        # Act & Assert - Student should access their own data
        user_response = client.get("/api/user", headers=student_headers)
        assert user_response.status_code == 200
        
        dashboard_response = client.get("/api/users/dashboard", headers=student_headers)
        assert dashboard_response.status_code == 200
        
        # Act & Assert - Student should NOT access admin endpoints
        admin_users_response = client.get("/api/users", headers=student_headers)
        assert admin_users_response.status_code == 403
        
        admin_overview_response = client.get("/api/academic/overview", headers=student_headers)
        assert admin_overview_response.status_code == 403

    def test_lecturer_access_restrictions(self, client, sample_lecturer, auth_manager):
        """Test that lecturers can access lecturer-specific endpoints but not admin endpoints"""
        # Arrange
        lecturer_headers = {"Authorization": f"Bearer {auth_manager.create_access_token(sample_lecturer.id)}"}
        
        # Act & Assert - Lecturer should access their own data
        user_response = client.get("/api/user", headers=lecturer_headers)
        assert user_response.status_code == 200
        
        courses_response = client.get("/api/lecturer/courses", headers=lecturer_headers)
        assert courses_response.status_code == 200
        
        # Act & Assert - Lecturer should NOT access admin-only endpoints
        admin_users_response = client.get("/api/users", headers=lecturer_headers)
        assert admin_users_response.status_code == 403
        
        create_user_response = client.post("/api/users", json={
            "name": "Test User",
            "email": "<EMAIL>",
            "password": "password",
            "role": "student"
        }, headers=lecturer_headers)
        assert create_user_response.status_code == 403

    def test_admin_full_access(self, client, sample_admin, auth_manager):
        """Test that admins can access all endpoints"""
        # Arrange
        admin_headers = {"Authorization": f"Bearer {auth_manager.create_access_token(sample_admin.id)}"}
        
        # Act & Assert - Admin should access all endpoints
        user_response = client.get("/api/user", headers=admin_headers)
        assert user_response.status_code == 200
        
        users_response = client.get("/api/users", headers=admin_headers)
        assert users_response.status_code == 200
        
        overview_response = client.get("/api/academic/overview", headers=admin_headers)
        assert overview_response.status_code == 200
        
        departments_response = client.get("/api/academic/departments", headers=admin_headers)
        assert users_response.status_code == 200

    def test_cross_role_data_access_security(self, client, sample_user, sample_lecturer, sample_admin, auth_manager):
        """Test that users cannot access other users' private data"""
        # Arrange
        student_headers = {"Authorization": f"Bearer {auth_manager.create_access_token(sample_user.id)}"}
        lecturer_headers = {"Authorization": f"Bearer {auth_manager.create_access_token(sample_lecturer.id)}"}
        
        # Act - Student tries to access lecturer's courses
        lecturer_courses_response = client.get("/api/lecturer/courses", headers=student_headers)
        
        # Act - Lecturer tries to access student enrollments for another student
        student_enrollments_response = client.get("/api/student/enrollments", headers=lecturer_headers)
        
        # Assert
        assert lecturer_courses_response.status_code == 403
        assert student_enrollments_response.status_code == 403

    def test_course_access_control(self, client, sample_course, sample_user, sample_lecturer, auth_manager, db_session):
        """Test course-specific access control"""
        # Arrange
        student_headers = {"Authorization": f"Bearer {auth_manager.create_access_token(sample_user.id)}"}
        lecturer_headers = {"Authorization": f"Bearer {auth_manager.create_access_token(sample_lecturer.id)}"}
        
        # Act - Student tries to access course materials without enrollment
        materials_response = client.get(
            f"/api/courses/{sample_course.id}/materials",
            headers=student_headers
        )
        
        # Assert - Should be denied access
        assert materials_response.status_code == 403
        
        # Act - Lecturer (who owns the course) accesses materials
        lecturer_materials_response = client.get(
            f"/api/courses/{sample_course.id}/materials",
            headers=lecturer_headers
        )
        
        # Assert - Should have access
        assert lecturer_materials_response.status_code == 200


class TestAuthenticationSecurity:
    """Test authentication security measures"""

    def test_password_hashing_security(self, auth_manager, db_session):
        """Test password hashing and verification security"""
        # Arrange
        password = "supersecretpassword123"
        
        # Act
        user = auth_manager.create_user(db_session, "Test User", "<EMAIL>", password)
        
        # Assert - Password should be hashed, not stored in plain text
        assert user.password_hash != password
        assert len(user.password_hash) > 50  # Bcrypt hashes are long
        assert user.password_hash.startswith("$2b$")  # Bcrypt identifier
        
        # Verify password verification works
        assert auth_manager.verify_password(password, user.password_hash) is True
        assert auth_manager.verify_password("wrongpassword", user.password_hash) is False

    def test_duplicate_email_prevention(self, auth_manager, db_session, sample_user):
        """Test prevention of duplicate email registration"""
        # Act & Assert
        with pytest.raises(ValueError, match="Email already registered"):
            auth_manager.create_user(
                db_session,
                "Another User",
                sample_user.email,  # Same email
                "differentpassword"
            )

    def test_authentication_failure_handling(self, auth_manager, db_session):
        """Test authentication failure scenarios"""
        # Test 1: Non-existent email
        with pytest.raises(ValueError, match="Invalid email or password"):
            auth_manager.authenticate_user(db_session, "<EMAIL>", "password")
        
        # Test 2: Wrong password for existing user
        user = auth_manager.create_user(db_session, "Test User", "<EMAIL>", "correctpassword")
        
        with pytest.raises(ValueError, match="Invalid email or password"):
            auth_manager.authenticate_user(db_session, user.email, "wrongpassword")

    def test_token_authentication_security(self, client, sample_user, auth_manager):
        """Test token-based authentication security"""
        # Test 1: No token provided
        response = client.get("/api/user")
        assert response.status_code == 401
        
        # Test 2: Invalid token format
        invalid_headers = {"Authorization": "Bearer invalid_token_format"}
        response = client.get("/api/user", headers=invalid_headers)
        assert response.status_code == 401
        
        # Test 3: Valid token
        valid_token = auth_manager.create_access_token(sample_user.id)
        valid_headers = {"Authorization": f"Bearer {valid_token}"}
        response = client.get("/api/user", headers=valid_headers)
        assert response.status_code == 200

    def test_session_security(self, client, auth_manager, sample_user):
        """Test session management security"""
        # Arrange
        login_data = {
            "email": sample_user.email,
            "password": "testpassword123"  # From fixture
        }
        
        # Act - Login
        login_response = client.post("/api/login", json=login_data)
        assert login_response.status_code == 200
        
        # Act - Logout
        logout_response = client.post("/api/logout")
        assert logout_response.status_code == 200
        
        # Verify logout message
        assert logout_response.json()["message"] == "Logged out successfully"


class TestInputValidationSecurity:
    """Test input validation and sanitization security"""

    def test_sql_injection_prevention(self, client, auth_manager, sample_admin):
        """Test SQL injection prevention in user inputs"""
        # Arrange
        admin_headers = {"Authorization": f"Bearer {auth_manager.create_access_token(sample_admin.id)}"}
        
        # Act - Try SQL injection in user creation
        malicious_data = {
            "name": "'; DROP TABLE users; --",
            "email": "<EMAIL>",
            "password": "password123",
            "role": "student"
        }
        
        response = client.post("/api/users", json=malicious_data, headers=admin_headers)
        
        # Assert - Should either succeed with escaped data or fail validation
        # The important thing is that it doesn't execute SQL injection
        assert response.status_code in [200, 400, 422]  # Not 500 (server error)

    def test_xss_prevention_in_inputs(self, client, auth_manager, sample_admin):
        """Test XSS prevention in user inputs"""
        # Arrange
        admin_headers = {"Authorization": f"Bearer {auth_manager.create_access_token(sample_admin.id)}"}
        
        # Act - Try XSS in department creation
        xss_data = {
            "name": "<script>alert('XSS')</script>Computer Science",
            "code": "CS",
            "description": "<img src=x onerror=alert('XSS')>Department description"
        }
        
        response = client.post("/api/academic/departments", json=xss_data, headers=admin_headers)
        
        # Assert - Should handle XSS attempts safely
        if response.status_code == 200:
            # If successful, verify the data is properly escaped/sanitized
            dept_data = response.json()["department"]
            assert "<script>" not in dept_data["name"]
            assert "onerror=" not in dept_data["description"]

    def test_input_length_validation(self, client, auth_manager, sample_admin):
        """Test input length validation security"""
        # Arrange
        admin_headers = {"Authorization": f"Bearer {auth_manager.create_access_token(sample_admin.id)}"}
        
        # Act - Try extremely long inputs
        long_string = "A" * 10000  # Very long string
        
        long_data = {
            "name": long_string,
            "email": f"{long_string}@test.com",
            "password": "password123",
            "role": "student"
        }
        
        response = client.post("/api/users", json=long_data, headers=admin_headers)
        
        # Assert - Should reject overly long inputs
        assert response.status_code in [400, 422]  # Validation error

    def test_email_format_validation(self, client):
        """Test email format validation security"""
        # Act - Try invalid email formats
        invalid_emails = [
            "notanemail",
            "@domain.com",
            "user@",
            "<EMAIL>",
            "user@domain",
            ""
        ]
        
        for invalid_email in invalid_emails:
            registration_data = {
                "name": "Test User",
                "email": invalid_email,
                "password": "password123"
            }
            
            response = client.post("/api/register", json=registration_data)
            
            # Assert - Should reject invalid email formats
            assert response.status_code in [400, 422]


class TestAuthorizationSecurity:
    """Test authorization security measures"""

    def test_unauthorized_access_prevention(self, client):
        """Test prevention of unauthorized access to protected endpoints"""
        # Test various protected endpoints without authentication
        protected_endpoints = [
            ("/api/user", "GET"),
            ("/api/users", "GET"),
            ("/api/academic/overview", "GET"),
            ("/api/lecturer/courses", "GET"),
            ("/api/student/enrollments", "GET")
        ]
        
        for endpoint, method in protected_endpoints:
            if method == "GET":
                response = client.get(endpoint)
            elif method == "POST":
                response = client.post(endpoint, json={})
            
            # Assert - Should require authentication
            assert response.status_code == 401

    def test_privilege_escalation_prevention(self, client, sample_user, auth_manager):
        """Test prevention of privilege escalation attacks"""
        # Arrange
        student_headers = {"Authorization": f"Bearer {auth_manager.create_access_token(sample_user.id)}"}
        
        # Act - Student tries to update their own role to admin
        escalation_data = {"role": "admin"}
        
        response = client.put(
            f"/api/users/{sample_user.id}",
            json=escalation_data,
            headers=student_headers
        )
        
        # Assert - Should be denied
        assert response.status_code == 403
