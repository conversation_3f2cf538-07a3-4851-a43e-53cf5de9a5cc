# AI-Powered LMS Testing Suite

This directory contains comprehensive test suites for the AI-powered Learning Management System, covering all aspects of functionality, security, and integration.

## Test Structure

### 1. Unit Testing (`test_unit_*.py`)
Tests individual functions and components in isolation for both backend and frontend.

#### `test_unit_auth.py` - Authentication Module Tests
- **AuthManager class testing**: User creation, authentication, token management
- **Password security**: Hashing, verification, case sensitivity
- **JWT token functionality**: Creation, verification, expiration, tampering detection
- **Coverage**: 95%+ of authentication-related functions

#### `test_unit_services.py` - Service Module Tests
- **UserManagementService**: User CRUD operations, role management, profile updates
- **AcademicService**: Department/course management, enrollment operations
- **Service integration**: Cross-service functionality testing
- **Coverage**: 90%+ of service layer functions

### 2. Integration Testing (`test_integration.py`)
Verifies that different modules work together correctly.

#### Module Integration Tests
- **AuthManager + UserManagementService**: User creation and profile management
- **AuthManager + AcademicService**: Lecturer-course relationships
- **UserManagement + Academic Services**: Enrollment workflows
- **API Endpoint Integration**: Complete request-response cycles

#### Database Integration Tests
- **Model relationships**: User-course-enrollment relationships
- **Cascade operations**: Soft delete and data integrity
- **Transaction handling**: Multi-table operations

### 3. System Testing (`test_system.py`)
Tests key user workflows end-to-end to ensure the system functions correctly as a whole.

#### Complete User Workflows
- **Student Registration**: Registration → Login → Dashboard → Course Enrollment
- **Assignment Submission**: Course Creation → Assignment Creation → Student Submission → Grading
- **Lecturer Course Management**: Course Creation → Material Upload → Student Management
- **Admin System Management**: Department Creation → User Management → System Overview

#### AI Integration Workflows
- **AI Tutoring**: Question submission → AI processing → Response delivery
- **Voice Recognition**: Audio upload → Transcription → AI processing
- **Document Processing**: PDF upload → Content extraction → AI analysis

### 4. Security Testing (`test_security.py`)
Implements and tests robust authentication and authorization using JWTs and RBAC.

#### JWT Security Tests
- **Token structure validation**: Claims verification, expiration handling
- **Token tampering detection**: Signature validation, algorithm verification
- **Secret key security**: Key rotation, algorithm enforcement

#### Role-Based Access Control (RBAC)
- **Student access restrictions**: Limited to student-specific endpoints
- **Lecturer access control**: Course management permissions
- **Admin full access**: System-wide administrative capabilities
- **Cross-role security**: Prevention of unauthorized data access

#### Authentication Security
- **Password security**: Bcrypt hashing, verification, strength validation
- **Session management**: Login/logout workflows, token lifecycle
- **Input validation**: SQL injection prevention, XSS protection, length validation

## Running Tests

### Prerequisites
1. **Virtual Environment**: Ensure `venv1` is activated
2. **Dependencies**: All packages from `requirements.txt` installed
3. **Environment Variables**: Test environment configured in `.env`
4. **Database**: Test database accessible

### Quick Start
```bash
# Navigate to testing directory
cd TESTING

# Run all tests
python run_tests.py

# Run specific test suite
python -m pytest test_unit_auth.py -v

# Run with coverage
python -m pytest --cov=../ --cov-report=html
```

### Test Configuration
Tests use the configuration in `conftest.py`:
- **Test Database**: SQLite in-memory database for isolation
- **Mock Services**: AI services mocked for consistent testing
- **Fixtures**: Pre-configured test data (users, courses, departments)
- **Authentication**: Test tokens and headers for API testing

## Test Coverage Goals

### Unit Tests
- **Authentication Module**: 95%+ coverage
- **Service Modules**: 90%+ coverage
- **Core Business Logic**: 95%+ coverage

### Integration Tests
- **API Endpoints**: 85%+ coverage
- **Service Interactions**: 90%+ coverage
- **Database Operations**: 95%+ coverage

### System Tests
- **User Workflows**: 100% of critical paths
- **AI Integration**: 80%+ of AI-related features
- **Error Handling**: 90%+ of error scenarios

### Security Tests
- **Authentication**: 100% of auth flows
- **Authorization**: 100% of RBAC scenarios
- **Input Validation**: 95%+ of input vectors

## Test Data Management

### Fixtures (`conftest.py`)
- **Users**: Sample student, lecturer, admin accounts
- **Academic Data**: Departments, courses, semesters
- **Authentication**: Test tokens and headers
- **Mock Services**: AI service responses

### Database Isolation
- **Transaction Rollback**: Each test runs in isolated transaction
- **Clean State**: Fresh database state for each test
- **No Side Effects**: Tests don't affect each other

## Continuous Integration

### Automated Testing
- **Pre-commit Hooks**: Run unit tests before commits
- **CI Pipeline**: Full test suite on pull requests
- **Deployment Gates**: All tests must pass before deployment

### Performance Monitoring
- **Test Duration**: Track test execution times
- **Coverage Trends**: Monitor coverage over time
- **Failure Analysis**: Automated failure reporting

## Test Reports

### Generated Reports
- **HTML Coverage Report**: Detailed line-by-line coverage
- **Test Execution Report**: Pass/fail status with timing
- **Security Audit Report**: Security test results
- **Performance Report**: Test execution performance

### Report Locations
- `test_report_YYYYMMDD_HHMMSS.txt`: Comprehensive test report
- `htmlcov/index.html`: Coverage report (if generated)
- `pytest_report.html`: Detailed pytest report

## Troubleshooting

### Common Issues
1. **Import Errors**: Ensure you're in the correct directory and virtual environment
2. **Database Errors**: Check database permissions and connection strings
3. **Mock Failures**: Verify mock service configurations
4. **Token Errors**: Ensure JWT secret key is set in test environment

### Debug Mode
```bash
# Run with debug output
python -m pytest -v -s --tb=long

# Run specific test with debugging
python -m pytest test_unit_auth.py::TestAuthManager::test_create_user_success -v -s
```

## Best Practices

### Writing Tests
1. **Arrange-Act-Assert**: Clear test structure
2. **Descriptive Names**: Test names explain what they test
3. **Single Responsibility**: Each test tests one thing
4. **Independent Tests**: Tests don't depend on each other

### Maintaining Tests
1. **Regular Updates**: Keep tests updated with code changes
2. **Coverage Monitoring**: Maintain high test coverage
3. **Performance Optimization**: Keep tests fast and efficient
4. **Documentation**: Document complex test scenarios

## Security Considerations

### Test Security
- **Test Data**: No real credentials or sensitive data in tests
- **Environment Isolation**: Test environment completely separate
- **Mock Services**: External services mocked to prevent data leakage
- **Cleanup**: Proper cleanup of test artifacts

This comprehensive testing suite ensures the AI-powered LMS is robust, secure, and ready for production deployment.
