"""
Unit Tests for Authentication Module
Tests individual functions and components in isolation for the backend authentication system.
"""
import pytest
import jwt
from datetime import datetime, timezone, timedelta
from unittest.mock import Mock, patch

from auth import AuthManager, pwd_context
from models import User, UserRole


class TestAuthManager:
    """Test cases for AuthManager class"""

    def test_create_user_success(self, db_session, auth_manager):
        """Test successful user creation"""
        # Arrange
        name = "<PERSON>"
        email = "<EMAIL>"
        password = "securepassword123"
        
        # Act
        user = auth_manager.create_user(db_session, name, email, password)
        
        # Assert
        assert user.name == name
        assert user.email == email
        assert user.role == UserRole.STUDENT  # Default role
        assert pwd_context.verify(password, user.password_hash)
        assert user.id is not None

    def test_create_user_with_role(self, db_session, auth_manager):
        """Test user creation with specific role"""
        # Arrange
        name = "<PERSON> Ad<PERSON>"
        email = "<EMAIL>"
        password = "adminpassword123"
        role = UserRole.ADMIN
        
        # Act
        user = auth_manager.create_user(db_session, name, email, password, role)
        
        # Assert
        assert user.role == UserRole.ADMIN
        assert user.name == name
        assert user.email == email

    def test_create_user_duplicate_email(self, db_session, auth_manager, sample_user):
        """Test user creation with duplicate email should fail"""
        # Arrange
        existing_email = sample_user.email
        
        # Act & Assert
        with pytest.raises(ValueError, match="Email already registered"):
            auth_manager.create_user(db_session, "New User", existing_email, "password123")

    def test_authenticate_user_success(self, db_session, auth_manager, test_user_data):
        """Test successful user authentication"""
        # Arrange
        user = auth_manager.create_user(
            db_session, 
            test_user_data["name"], 
            test_user_data["email"], 
            test_user_data["password"]
        )
        
        # Act
        authenticated_user = auth_manager.authenticate_user(
            db_session, 
            test_user_data["email"], 
            test_user_data["password"]
        )
        
        # Assert
        assert authenticated_user.id == user.id
        assert authenticated_user.email == test_user_data["email"]

    def test_authenticate_user_wrong_email(self, db_session, auth_manager):
        """Test authentication with non-existent email"""
        # Act & Assert
        with pytest.raises(ValueError, match="Invalid email or password"):
            auth_manager.authenticate_user(db_session, "<EMAIL>", "password")

    def test_authenticate_user_wrong_password(self, db_session, auth_manager, sample_user):
        """Test authentication with wrong password"""
        # Act & Assert
        with pytest.raises(ValueError, match="Invalid email or password"):
            auth_manager.authenticate_user(db_session, sample_user.email, "wrongpassword")

    def test_create_access_token(self, auth_manager):
        """Test access token creation"""
        # Arrange
        user_id = 123
        
        # Act
        token = auth_manager.create_access_token(user_id)
        
        # Assert
        assert isinstance(token, str)
        assert len(token) > 0
        
        # Decode and verify token content
        payload = jwt.decode(token, "test_secret_key", algorithms=["HS256"])
        assert payload["sub"] == str(user_id)
        assert payload["type"] == "access"
        assert "exp" in payload

    def test_create_refresh_token(self, auth_manager):
        """Test refresh token creation"""
        # Arrange
        user_id = 456
        
        # Act
        token = auth_manager.create_refresh_token(user_id)
        
        # Assert
        assert isinstance(token, str)
        assert len(token) > 0
        
        # Decode and verify token content
        payload = jwt.decode(token, "test_secret_key", algorithms=["HS256"])
        assert payload["sub"] == str(user_id)
        assert payload["type"] == "refresh"

    def test_verify_token_valid(self, auth_manager):
        """Test token verification with valid token"""
        # Arrange
        user_id = 789
        token = auth_manager.create_access_token(user_id)
        
        # Act
        verified_user_id, token_type = auth_manager.verify_token(token)
        
        # Assert
        assert verified_user_id == user_id
        assert token_type == "access"

    def test_verify_token_invalid(self, auth_manager):
        """Test token verification with invalid token"""
        # Arrange
        invalid_token = "invalid.token.here"
        
        # Act
        user_id, token_type = auth_manager.verify_token(invalid_token)
        
        # Assert
        assert user_id is None
        assert token_type is None

    def test_verify_token_expired(self, auth_manager):
        """Test token verification with expired token"""
        # Arrange - Create token that expires immediately
        user_id = 999
        expire = datetime.now(timezone.utc) - timedelta(minutes=1)  # Already expired
        payload = {
            "sub": str(user_id),
            "exp": expire,
            "type": "access"
        }
        expired_token = jwt.encode(payload, "test_secret_key", algorithm="HS256")
        
        # Act
        verified_user_id, token_type = auth_manager.verify_token(expired_token)
        
        # Assert
        assert verified_user_id is None
        assert token_type is None

    def test_get_user_by_id_exists(self, db_session, auth_manager, sample_user):
        """Test getting user by ID when user exists"""
        # Act
        user = auth_manager.get_user_by_id(db_session, sample_user.id)
        
        # Assert
        assert user is not None
        assert user.id == sample_user.id
        assert user.email == sample_user.email

    def test_get_user_by_id_not_exists(self, db_session, auth_manager):
        """Test getting user by ID when user doesn't exist"""
        # Act
        user = auth_manager.get_user_by_id(db_session, 99999)
        
        # Assert
        assert user is None

    def test_verify_password_correct(self, auth_manager):
        """Test password verification with correct password"""
        # Arrange
        plain_password = "testpassword123"
        hashed_password = pwd_context.hash(plain_password)
        
        # Act
        is_valid = auth_manager.verify_password(plain_password, hashed_password)
        
        # Assert
        assert is_valid is True

    def test_verify_password_incorrect(self, auth_manager):
        """Test password verification with incorrect password"""
        # Arrange
        plain_password = "testpassword123"
        wrong_password = "wrongpassword"
        hashed_password = pwd_context.hash(plain_password)
        
        # Act
        is_valid = auth_manager.verify_password(wrong_password, hashed_password)
        
        # Assert
        assert is_valid is False

    def test_hash_password(self, auth_manager):
        """Test password hashing"""
        # Arrange
        password = "mypassword123"
        
        # Act
        hashed = auth_manager.hash_password(password)
        
        # Assert
        assert hashed != password  # Should be different from plain text
        assert len(hashed) > 0
        assert pwd_context.verify(password, hashed)  # Should verify correctly


class TestPasswordContext:
    """Test cases for password hashing context"""

    def test_hash_password_different_each_time(self):
        """Test that hashing the same password produces different hashes"""
        # Arrange
        password = "samepassword"
        
        # Act
        hash1 = pwd_context.hash(password)
        hash2 = pwd_context.hash(password)
        
        # Assert
        assert hash1 != hash2  # Different salts should produce different hashes
        assert pwd_context.verify(password, hash1)
        assert pwd_context.verify(password, hash2)

    def test_verify_password_case_sensitive(self):
        """Test that password verification is case sensitive"""
        # Arrange
        password = "CaseSensitive"
        hashed = pwd_context.hash(password)
        
        # Act & Assert
        assert pwd_context.verify("CaseSensitive", hashed) is True
        assert pwd_context.verify("casesensitive", hashed) is False
        assert pwd_context.verify("CASESENSITIVE", hashed) is False


class TestJWTTokens:
    """Test cases for JWT token functionality"""

    def test_token_contains_correct_claims(self, auth_manager):
        """Test that JWT tokens contain the correct claims"""
        # Arrange
        user_id = 12345
        
        # Act
        access_token = auth_manager.create_access_token(user_id)
        refresh_token = auth_manager.create_refresh_token(user_id)
        
        # Assert
        access_payload = jwt.decode(access_token, "test_secret_key", algorithms=["HS256"])
        refresh_payload = jwt.decode(refresh_token, "test_secret_key", algorithms=["HS256"])
        
        # Check access token
        assert access_payload["sub"] == str(user_id)
        assert access_payload["type"] == "access"
        assert "exp" in access_payload
        
        # Check refresh token
        assert refresh_payload["sub"] == str(user_id)
        assert refresh_payload["type"] == "refresh"
        assert "exp" in refresh_payload

    def test_token_expiration_times(self, auth_manager):
        """Test that tokens have appropriate expiration times"""
        # Arrange
        user_id = 54321
        current_time = datetime.now(timezone.utc)
        
        # Act
        access_token = auth_manager.create_access_token(user_id)
        refresh_token = auth_manager.create_refresh_token(user_id)
        
        # Assert
        access_payload = jwt.decode(access_token, "test_secret_key", algorithms=["HS256"])
        refresh_payload = jwt.decode(refresh_token, "test_secret_key", algorithms=["HS256"])
        
        access_exp = datetime.fromtimestamp(access_payload["exp"], tz=timezone.utc)
        refresh_exp = datetime.fromtimestamp(refresh_payload["exp"], tz=timezone.utc)
        
        # Access token should expire after refresh token (in this implementation)
        # But both should be in the future
        assert access_exp > current_time
        assert refresh_exp > current_time
