"""
Integration Tests for AI-powered LMS
Tests that verify different modules work together correctly.
"""
import pytest
import json
from unittest.mock import patch, Mock

from auth import AuthManager
from services.user_management_service import UserManagementService
from services.academic_service import AcademicService
from models import User, UserRole, Course, Enrollment, EnrollmentStatus


class TestAuthManagerIntegration:
    """Test AuthManager integration with other components"""

    def test_auth_manager_with_user_management_service(self, db_session):
        """Test AuthManager working with UserManagementService"""
        # Arrange
        auth_manager = AuthManager()
        user_service = UserManagementService()
        
        # Act - Create user through AuthManager
        user = auth_manager.create_user(
            db_session, 
            "Integration Test User", 
            "<EMAIL>", 
            "password123"
        )
        
        # Get user through UserManagementService
        user_profile = user_service.get_user_profile(db_session, user.id)
        
        # Assert
        assert user_profile["id"] == user.id
        assert user_profile["email"] == user.email
        assert user_profile["name"] == user.name

    def test_auth_manager_with_academic_service(self, db_session, sample_department, sample_semester):
        """Test AuthManager working with AcademicService"""
        # Arrange
        auth_manager = AuthManager()
        academic_service = AcademicService()
        
        # Create lecturer through AuthManager
        lecturer = auth_manager.create_user(
            db_session,
            "Test Lecturer",
            "<EMAIL>",
            "lecturerpass123",
            UserRole.LECTURER
        )
        
        # Create course through AcademicService
        course_data = {
            "name": "Integration Test Course",
            "code": "INT101",
            "description": "Course for integration testing",
            "credits": 3,
            "department_id": sample_department.id,
            "lecturer_id": lecturer.id,
            "semester_id": sample_semester.id,
            "max_capacity": 25
        }
        
        # Act
        course = Course(**course_data)
        db_session.add(course)
        db_session.commit()
        db_session.refresh(course)
        
        # Get courses for lecturer
        lecturer_courses = academic_service.get_courses(db_session, lecturer_id=lecturer.id)
        
        # Assert
        assert len(lecturer_courses) >= 1
        course_names = [c["name"] for c in lecturer_courses]
        assert "Integration Test Course" in course_names

    def test_token_verification_with_user_retrieval(self, db_session, auth_manager, sample_user):
        """Test token verification integrated with user retrieval"""
        # Arrange
        token = auth_manager.create_access_token(sample_user.id)
        
        # Act
        user_id, token_type = auth_manager.verify_token(token)
        retrieved_user = auth_manager.get_user_by_id(db_session, user_id)
        
        # Assert
        assert user_id == sample_user.id
        assert token_type == "access"
        assert retrieved_user.id == sample_user.id
        assert retrieved_user.email == sample_user.email


class TestUserManagementServiceIntegration:
    """Test UserManagementService integration with other services"""

    def test_user_service_with_academic_service_enrollment(self, db_session, sample_user, sample_course):
        """Test user service integration with academic service for enrollment"""
        # Arrange
        user_service = UserManagementService()
        academic_service = AcademicService()
        
        # Act - Enroll student
        enrollment_result = academic_service.enroll_student(
            db_session, 
            sample_user.id, 
            sample_course.id, 
            1  # program_id
        )
        
        # Get student dashboard
        dashboard = user_service.get_student_dashboard(db_session, sample_user.id)
        
        # Assert
        assert enrollment_result["success"] is True
        assert "enrolled_courses" in dashboard
        assert len(dashboard["enrolled_courses"]) >= 1

    def test_lecturer_dashboard_integration(self, db_session, sample_lecturer, sample_course, sample_user):
        """Test lecturer dashboard integration with multiple services"""
        # Arrange
        user_service = UserManagementService()
        academic_service = AcademicService()
        
        # Enroll a student in lecturer's course
        academic_service.enroll_student(db_session, sample_user.id, sample_course.id, 1)
        
        # Act
        dashboard = user_service.get_lecturer_dashboard(db_session, sample_lecturer.id)
        
        # Assert
        assert "courses" in dashboard
        assert "total_students" in dashboard
        assert "recent_submissions" in dashboard
        assert dashboard["total_students"] >= 1

    def test_user_role_change_integration(self, db_session, sample_user):
        """Test user role change integration across services"""
        # Arrange
        user_service = UserManagementService()
        academic_service = AcademicService()
        
        # Act - Change user role from student to lecturer
        updated_user = user_service.update_user(
            db_session, 
            sample_user.id, 
            {"role": "lecturer"}
        )
        
        # Get users by role
        lecturers = user_service.get_users_by_role(db_session, UserRole.LECTURER)
        students = user_service.get_users_by_role(db_session, UserRole.STUDENT)
        
        # Assert
        assert updated_user["role"] == "lecturer"
        lecturer_ids = [l["id"] for l in lecturers]
        student_ids = [s["id"] for s in students]
        assert sample_user.id in lecturer_ids
        assert sample_user.id not in student_ids


class TestAcademicServiceIntegration:
    """Test AcademicService integration with other components"""

    def test_course_creation_with_user_validation(self, db_session, sample_lecturer, sample_department, sample_semester):
        """Test course creation with lecturer validation"""
        # Arrange
        academic_service = AcademicService()
        
        course_data = {
            "name": "Advanced Programming",
            "code": "CS201",
            "description": "Advanced programming concepts",
            "credits": 4,
            "department_id": sample_department.id,
            "lecturer_id": sample_lecturer.id,
            "semester_id": sample_semester.id,
            "max_capacity": 20
        }
        
        # Act
        course = Course(**course_data)
        db_session.add(course)
        db_session.commit()
        db_session.refresh(course)
        
        # Verify course is associated with lecturer
        lecturer_courses = academic_service.get_courses(db_session, lecturer_id=sample_lecturer.id)
        
        # Assert
        assert len(lecturer_courses) >= 1
        course_codes = [c["code"] for c in lecturer_courses]
        assert "CS201" in course_codes

    def test_enrollment_workflow_integration(self, db_session, sample_user, sample_course):
        """Test complete enrollment workflow integration"""
        # Arrange
        academic_service = AcademicService()
        
        # Act - Complete enrollment workflow
        # 1. Enroll student
        enrollment_result = academic_service.enroll_student(
            db_session, 
            sample_user.id, 
            sample_course.id, 
            1
        )
        
        # 2. Get student enrollments
        enrollments = academic_service.get_student_enrollments(db_session, sample_user.id)
        
        # 3. Get course with enrollment data
        courses = academic_service.get_courses(db_session)
        
        # Assert
        assert enrollment_result["success"] is True
        assert len(enrollments) >= 1
        assert enrollments[0]["course_id"] == sample_course.id
        assert enrollments[0]["student_id"] == sample_user.id

    def test_academic_overview_integration(self, db_session, sample_user, sample_lecturer, sample_admin, sample_course, sample_department):
        """Test academic overview with real data integration"""
        # Arrange
        academic_service = AcademicService()
        
        # Enroll student to get realistic data
        academic_service.enroll_student(db_session, sample_user.id, sample_course.id, 1)
        
        # Act
        overview = academic_service.get_academic_overview(db_session)
        
        # Assert
        assert overview["total_students"] >= 1
        assert overview["total_lecturers"] >= 1
        assert overview["total_courses"] >= 1
        assert overview["total_departments"] >= 1
        assert "current_semester" in overview


class TestAPIEndpointIntegration:
    """Test API endpoints integration with services"""

    def test_register_login_workflow(self, client):
        """Test complete user registration and login workflow"""
        # Arrange
        user_data = {
            "name": "API Test User",
            "email": "<EMAIL>",
            "password": "apipassword123"
        }
        
        # Act - Register user
        register_response = client.post("/api/register", json=user_data)
        
        # Login with same credentials
        login_data = {
            "email": user_data["email"],
            "password": user_data["password"]
        }
        login_response = client.post("/api/login", json=login_data)
        
        # Assert
        assert register_response.status_code == 200
        assert login_response.status_code == 200
        
        register_data = register_response.json()
        login_data = login_response.json()
        
        assert register_data["user"]["email"] == user_data["email"]
        assert login_data["user"]["email"] == user_data["email"]
        assert register_data["user"]["id"] == login_data["user"]["id"]

    def test_authenticated_api_access(self, client, sample_user, auth_manager):
        """Test authenticated API access integration"""
        # Arrange
        token = auth_manager.create_access_token(sample_user.id)
        headers = {"Authorization": f"Bearer {token}"}
        
        # Act
        response = client.get("/api/user", headers=headers)
        
        # Assert
        assert response.status_code == 200
        user_data = response.json()
        assert user_data["user"]["id"] == sample_user.id
        assert user_data["user"]["email"] == sample_user.email

    @patch('services.gemini_service.GeminiService.get_response')
    def test_ai_tutor_integration(self, mock_gemini, client, authenticated_headers):
        """Test AI tutor integration with authentication"""
        # Arrange
        mock_gemini.return_value = {
            "text": "This is a test AI response",
            "hasChart": False,
            "hasCode": False
        }
        
        question_data = {"question": "What is Python?"}
        
        # Act
        response = client.post("/api/ask", json=question_data, headers=authenticated_headers)
        
        # Assert
        assert response.status_code == 200
        ai_response = response.json()
        assert "text" in ai_response
        assert ai_response["text"] == "This is a test AI response"
        mock_gemini.assert_called_once_with("What is Python?")

    def test_course_management_integration(self, client, admin_headers, sample_department, sample_semester):
        """Test course management API integration"""
        # Arrange
        course_data = {
            "name": "API Integration Course",
            "code": "API101",
            "description": "Course created via API",
            "credits": 3,
            "department_id": sample_department.id,
            "semester_id": sample_semester.id,
            "max_capacity": 30
        }
        
        # Act
        create_response = client.post("/api/courses", json=course_data, headers=admin_headers)
        get_response = client.get("/api/academic/courses", headers=admin_headers)
        
        # Assert
        assert create_response.status_code == 200
        assert get_response.status_code == 200
        
        courses = get_response.json()["courses"]
        course_codes = [course["code"] for course in courses]
        assert "API101" in course_codes


class TestDatabaseIntegration:
    """Test database integration across different models"""

    def test_user_course_relationship_integration(self, db_session, sample_user, sample_lecturer, sample_course):
        """Test user-course relationship integration"""
        # Arrange
        academic_service = AcademicService()
        
        # Act - Enroll student
        academic_service.enroll_student(db_session, sample_user.id, sample_course.id, 1)
        
        # Query relationships
        enrollment = db_session.query(Enrollment).filter(
            Enrollment.student_id == sample_user.id,
            Enrollment.course_id == sample_course.id
        ).first()
        
        # Assert
        assert enrollment is not None
        assert enrollment.student.id == sample_user.id
        assert enrollment.course.id == sample_course.id
        assert enrollment.course.lecturer.id == sample_lecturer.id

    def test_cascade_operations_integration(self, db_session, sample_department, sample_course):
        """Test cascade operations between related models"""
        # Arrange
        academic_service = AcademicService()
        
        # Act - Soft delete department
        academic_service.delete_department(db_session, sample_department.id)
        
        # Assert
        db_session.refresh(sample_department)
        db_session.refresh(sample_course)
        
        assert sample_department.is_active is False
        # Course should still exist but department relationship should be maintained
        assert sample_course.department_id == sample_department.id
