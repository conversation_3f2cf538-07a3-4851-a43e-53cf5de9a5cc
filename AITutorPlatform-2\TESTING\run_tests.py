#!/usr/bin/env python3
"""
Test Runner for AI-powered LMS
Runs all test suites and generates comprehensive test reports.
"""
import os
import sys
import subprocess
import time
from datetime import datetime

# Add parent directory to path to import modules
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def run_test_suite(test_file, description):
    """Run a specific test suite and return results"""
    print(f"\n{'='*60}")
    print(f"Running {description}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        # Run pytest with verbose output and coverage
        result = subprocess.run([
            'python', '-m', 'pytest', 
            test_file, 
            '-v',  # Verbose output
            '--tb=short',  # Short traceback format
            '--durations=10',  # Show 10 slowest tests
            '--color=yes'  # Colored output
        ], capture_output=True, text=True, cwd=os.path.dirname(__file__))
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"Duration: {duration:.2f} seconds")
        print(f"Return code: {result.returncode}")
        
        if result.stdout:
            print("\nSTDOUT:")
            print(result.stdout)
        
        if result.stderr:
            print("\nSTDERR:")
            print(result.stderr)
        
        return {
            'test_file': test_file,
            'description': description,
            'duration': duration,
            'return_code': result.returncode,
            'stdout': result.stdout,
            'stderr': result.stderr,
            'success': result.returncode == 0
        }
        
    except Exception as e:
        print(f"Error running {test_file}: {str(e)}")
        return {
            'test_file': test_file,
            'description': description,
            'duration': 0,
            'return_code': -1,
            'stdout': '',
            'stderr': str(e),
            'success': False
        }

def generate_test_report(results):
    """Generate a comprehensive test report"""
    print(f"\n{'='*80}")
    print("COMPREHENSIVE TEST REPORT")
    print(f"{'='*80}")
    print(f"Generated at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r['success'])
    failed_tests = total_tests - successful_tests
    total_duration = sum(r['duration'] for r in results)
    
    print(f"\nSUMMARY:")
    print(f"Total Test Suites: {total_tests}")
    print(f"Successful: {successful_tests}")
    print(f"Failed: {failed_tests}")
    print(f"Success Rate: {(successful_tests/total_tests)*100:.1f}%")
    print(f"Total Duration: {total_duration:.2f} seconds")
    
    print(f"\nDETAILED RESULTS:")
    for result in results:
        status = "✅ PASSED" if result['success'] else "❌ FAILED"
        print(f"{status} {result['description']} ({result['duration']:.2f}s)")
        
        if not result['success']:
            print(f"   Error: {result['stderr'][:200]}...")
    
    # Generate recommendations
    print(f"\nRECOMMENDations:")
    if failed_tests == 0:
        print("🎉 All tests passed! Your LMS is ready for deployment.")
    else:
        print(f"⚠️  {failed_tests} test suite(s) failed. Please review and fix issues before deployment.")
        print("   - Check error messages above for specific issues")
        print("   - Ensure all dependencies are installed")
        print("   - Verify database connections and environment variables")
    
    return {
        'total_tests': total_tests,
        'successful_tests': successful_tests,
        'failed_tests': failed_tests,
        'success_rate': (successful_tests/total_tests)*100,
        'total_duration': total_duration
    }

def main():
    """Main test runner function"""
    print("AI-Powered LMS Test Suite Runner")
    print("=" * 50)
    
    # Define test suites
    test_suites = [
        ('test_unit_auth.py', 'Unit Tests - Authentication Module'),
        ('test_unit_services.py', 'Unit Tests - Service Modules'),
        ('test_integration.py', 'Integration Tests - Module Interactions'),
        ('test_system.py', 'System Tests - End-to-End Workflows'),
        ('test_security.py', 'Security Tests - Authentication & Authorization')
    ]
    
    # Check if we're in the right directory
    if not os.path.exists('conftest.py'):
        print("Error: Please run this script from the TESTING directory")
        sys.exit(1)
    
    # Run all test suites
    results = []
    for test_file, description in test_suites:
        if os.path.exists(test_file):
            result = run_test_suite(test_file, description)
            results.append(result)
        else:
            print(f"Warning: Test file {test_file} not found, skipping...")
    
    # Generate comprehensive report
    report = generate_test_report(results)
    
    # Save report to file
    report_file = f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    with open(report_file, 'w') as f:
        f.write(f"AI-Powered LMS Test Report\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write(f"Summary:\n")
        f.write(f"Total Test Suites: {report['total_tests']}\n")
        f.write(f"Successful: {report['successful_tests']}\n")
        f.write(f"Failed: {report['failed_tests']}\n")
        f.write(f"Success Rate: {report['success_rate']:.1f}%\n")
        f.write(f"Total Duration: {report['total_duration']:.2f} seconds\n\n")
        
        for result in results:
            f.write(f"\n{result['description']}:\n")
            f.write(f"Status: {'PASSED' if result['success'] else 'FAILED'}\n")
            f.write(f"Duration: {result['duration']:.2f}s\n")
            if result['stdout']:
                f.write(f"Output:\n{result['stdout']}\n")
            if result['stderr']:
                f.write(f"Errors:\n{result['stderr']}\n")
    
    print(f"\nDetailed report saved to: {report_file}")
    
    # Exit with appropriate code
    if report['failed_tests'] > 0:
        print(f"\n❌ Testing completed with {report['failed_tests']} failures")
        sys.exit(1)
    else:
        print(f"\n✅ All tests passed successfully!")
        sys.exit(0)

if __name__ == "__main__":
    main()
